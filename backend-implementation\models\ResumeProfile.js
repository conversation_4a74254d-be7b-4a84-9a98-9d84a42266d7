// models/ResumeProfile.js
const mongoose = require('mongoose');

// Sub-schemas for complex fields
const ProfileSchema = new mongoose.Schema({
  Network: { type: String, required: true },
  Username: { type: String, required: true },
  ProfileLink: { type: String, required: true },
  ProfileImage: { type: String }
}, { _id: true });

const ExperienceSchema = new mongoose.Schema({
  Position: { type: String, required: true },
  Company: { type: String, required: true },
  StartDate: { type: String, required: true },
  EndDate: { type: String }, // Optional for current positions
  Description: { type: String },
  Location: { type: String }
}, { _id: true });

const EducationSchema = new mongoose.Schema({
  Institution: { type: String, required: true },
  Degree: { type: String, required: true },
  Field: { type: String },
  StartDate: { type: String },
  EndDate: { type: String },
  Grade: { type: String },
  Description: { type: String }
}, { _id: true });

const SkillSchema = new mongoose.Schema({
  Skill: { type: String, required: true },
  Proficiency: { type: String, enum: ['Beginner', 'Intermediate', 'Advanced', 'Expert'], default: 'Intermediate' },
  Keywords: [{ type: String }],
  Description: { type: String }
}, { _id: true });

const LanguageSchema = new mongoose.Schema({
  Language: { type: String, required: true },
  Proficiency: { type: String, enum: ['Basic', 'Conversational', 'Fluent', 'Native'], default: 'Conversational' }
}, { _id: true });

const CertificationSchema = new mongoose.Schema({
  Name: { type: String, required: true },
  Issuer: { type: String, required: true },
  Date: { type: String },
  ExpiryDate: { type: String },
  CredentialID: { type: String },
  CredentialURL: { type: String }
}, { _id: true });

const AwardSchema = new mongoose.Schema({
  Title: { type: String, required: true },
  Issuer: { type: String, required: true },
  Date: { type: String },
  Description: { type: String }
}, { _id: true });

const ProjectSchema = new mongoose.Schema({
  Name: { type: String, required: true },
  Description: { type: String },
  StartDate: { type: String },
  EndDate: { type: String },
  URL: { type: String },
  Technologies: [{ type: String }]
}, { _id: true });

const PublicationSchema = new mongoose.Schema({
  Title: { type: String, required: true },
  Publisher: { type: String },
  Date: { type: String },
  URL: { type: String },
  Description: { type: String }
}, { _id: true });

const VolunteeringSchema = new mongoose.Schema({
  Organization: { type: String, required: true },
  Role: { type: String, required: true },
  StartDate: { type: String },
  EndDate: { type: String },
  Description: { type: String }
}, { _id: true });

const ReferenceSchema = new mongoose.Schema({
  Name: { type: String, required: true },
  Position: { type: String },
  Company: { type: String },
  Email: { type: String },
  Phone: { type: String },
  Relationship: { type: String }
}, { _id: true });

// Main Resume Profile Schema
const ResumeProfileSchema = new mongoose.Schema({
  // Basic Information
  Title: { type: String, required: true },
  Email: { type: String, required: true },
  Summary: { type: String }, // Note: Also support 'summery' for backward compatibility
  summery: { type: String }, // Backward compatibility
  
  // Profile Settings
  Template: { type: String, default: 'default' },
  isPublished: { type: Boolean, default: false },
  PublishURL: { type: String },
  PublicAccess: { type: Boolean, default: false },
  
  // Metadata
  Tags: [{ type: String }],
  Order: [{ type: String }],
  
  // Contact Information
  Headline: { type: String },
  Website: { type: String },
  Phone: { type: String },
  Location: { type: String },
  ProfilePic: { type: String },
  
  // File Management
  PDFLink: { type: String },
  ProfileImagePublicId: { type: String },
  LastExported: { type: Date },
  
  // Profile Sections
  Profiles: [ProfileSchema],
  Experience: [ExperienceSchema],
  Education: [EducationSchema],
  Skills: [SkillSchema],
  Languages: [LanguageSchema],
  Awards: [AwardSchema],
  Certifications: [CertificationSchema],
  Interests: [{ type: String }],
  Publications: [PublicationSchema],
  Volunteering: [VolunteeringSchema],
  References: [ReferenceSchema],
  Projects: [ProjectSchema]
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for performance
ResumeProfileSchema.index({ Email: 1, Title: 1 }, { unique: true });
ResumeProfileSchema.index({ Email: 1, createdAt: -1 });
ResumeProfileSchema.index({ isPublished: 1 });

// Virtual for backward compatibility with 'summery' field
ResumeProfileSchema.virtual('summaryText').get(function() {
  return this.Summary || this.summery || '';
});

// Pre-save middleware to handle Summary/summery compatibility
ResumeProfileSchema.pre('save', function(next) {
  // If Summary is provided but summery is not, copy Summary to summery
  if (this.Summary && !this.summery) {
    this.summery = this.Summary;
  }
  // If summery is provided but Summary is not, copy summery to Summary
  if (this.summery && !this.Summary) {
    this.Summary = this.summery;
  }
  next();
});

module.exports = mongoose.model('ResumeProfile', ResumeProfileSchema);
